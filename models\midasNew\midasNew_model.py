import torch
import math
import torch.nn as nn

import torch.nn.functional as F
from torch.distributions import Laplace, Normal
from pythae.models.base.base_utils import ModelOutput

from models.base import BaseModel
from models.nn.base_arch import Discriminator
from collections import defaultdict

import random
from itertools import combinations

#---------------------------------------------------------------------
def exp(x, eps=1e-12):
    return (x < 0) * (x.clamp(max=0)).exp() + (x >= 0) / ((-x.clamp(min=0)).exp() + eps)

def log(x, eps=1e-12):
    return (x + eps).log()

def poe(mus, logvars):
    """
    Product of Experts
    - mus: [mu_1, ..., mu_M], where mu_m is N * K
    - logvars: [logvar_1, ..., logvar_M], where logvar_m is N * K
    """
    
    # mus = [torch.full_like(mus[0], 0)] + mus
    # logvars = [torch.full_like(logvars[0], 0)] + logvars
    
    mus_stack = torch.stack(mus, dim=1)  # N * M * K
    logvars_stack = torch.stack(logvars, dim=1)
    
    T = exp(-logvars_stack)  # precision of i-th Gaussian expert at point x
    T_sum = T.sum(1)  # N * K
    pd_mu = (mus_stack * T).sum(1) / T_sum
    pd_var = 1 / T_sum
    pd_logvar = log(pd_var)
    return pd_mu, pd_logvar  # N * K

def sample_gaussian(mu, logvar):
    std = (0.5*logvar).exp()
    eps = torch.randn_like(std)
    return mu + std*eps

def calc_kld_loss(mu, logvar):
    return (-0.5 * (1 + logvar - mu.pow(2) - logvar.exp())).sum() / mu.size(0)
#---------------------------------------------------------------------


class MIDASNEW_DIS(BaseModel):
    def __init__(self, model_config, encoders, decoders):
        super().__init__(model_config, encoders, decoders)


        # Set the priors for shared and private spaces.
        self.mean_priors = torch.nn.ParameterDict()
        self.logvars_priors = torch.nn.ParameterDict()
        self.beta = model_config.beta
        self.modalities_specific_dim = model_config.modalities_specific_dim
        self.reconstruction_option = model_config.reconstruction_option
        self.multiple_latent_spaces = True
        self.style_dims = {m: self.modalities_specific_dim for m in self.encoders}

        # modality specific priors (referred to as r distributions in paper)
        for mod in list(self.encoders.keys()):
            self.mean_priors[mod] = torch.nn.Parameter(
                torch.zeros(1, model_config.modalities_specific_dim),
                requires_grad=True,
            )
            self.logvars_priors[mod] = torch.nn.Parameter(
                torch.zeros(1, model_config.modalities_specific_dim),
                requires_grad=model_config.learn_modality_prior,
            )
   
        self.objective = model_config.loss

        # -------------discriminator-------------------
        self.len_subj = model_config.len_subj
        input_size = model_config.latent_dim

        if self.multiple_latent_spaces:
            self.discriminator_modality = nn.ModuleDict()
            if model_config.len_subj_fmri != 0:
                self.discriminator_modality['fmri'] = Discriminator(model_config.len_subj_fmri, input_size)
            if model_config.len_subj_eeg != 0:
                self.discriminator_modality['eeg'] = Discriminator(model_config.len_subj_eeg, input_size)
            self.discriminator_modality['fusion'] = Discriminator(model_config.len_subj_eeg, input_size-model_config.modalities_specific_dim)


    def compute_posteriors_and_embeddings(self, inputs):

        # ------------ encodings for all modalities------------
        embeddings = {}
        reconstructions = {}
        c_mu = {}
        e_mu = {}

        if 'subj_index' in inputs.keys():
            s = nn.functional.one_hot(inputs['subj_index'], num_classes=self.len_subj).float()
        
        temp = {}
        for cond_mod in inputs:
            if cond_mod in self.used_modalities:
                modality_input = inputs[cond_mod]
                if cond_mod in ['fmri', 'eeg']:
                    modality_input = torch.cat([modality_input, s], 1)
                output = self.encoding(modality_input, cond_mod, style_dim=self.modalities_specific_dim, subj_dim=self.model_config.sub_dim)
                temp[cond_mod] = output
       

        #---------------------c poe fusion (share)-------------------------
        c_mu_list = []
        c_logvar_list = []
        for key, value in temp.items():
            c_mu_list.append(value['embedding'])
            c_logvar_list.append(value['log_covariance'])
        c_fusion_mu, c_fusion_logvar = poe(c_mu_list, c_logvar_list)
        if self.training:
            c_fusion = sample_gaussian(c_fusion_mu, c_fusion_logvar)
        else:
            c_fusion = c_fusion_mu


        #---------------------masked fusion (share)-------------------------
        tem_modality = temp.keys()
        embeddings['masked_fusion'] = defaultdict(dict)
        def get_filtered_combinations(modality_list):
            # 获取所有非空子集（长度从 1 到 len(modality_list)）
            all_combinations = []
            for r in range(1, len(modality_list) + 1):
                # 生成长度为 r 的组合
                combs = list(combinations(modality_list, r))
                all_combinations.extend(combs)
            
            # 过滤掉单个元素子集和全集
            filtered_combinations = [
                list(comb) for comb in all_combinations
                if len(comb) > 1 and len(comb) < len(modality_list)
            ]
            
            return filtered_combinations

        filtered_combinations = get_filtered_combinations(tem_modality)
        for comb in filtered_combinations:
            masked_mu_list = []
            masked_logvar_list = []
            for mod in comb:
                masked_mu_list.append(temp[mod]['embedding'])
                masked_logvar_list.append(temp[mod]['log_covariance'])
            masked_fusion_mu, masked_fusion_logvar = poe(masked_mu_list, masked_logvar_list)
            masked_fusion = sample_gaussian(masked_fusion_mu, masked_fusion_logvar)
            embeddings['masked_fusion']['_'.join(comb)] = masked_fusion


        # ----------- decoding from one modality---------------
        for cond_mod in inputs:
            if cond_mod in self.used_modalities:
                output = temp[cond_mod]
                mu, log_var = output['embedding'], output['log_covariance']
                mu_style = output['style_embedding']
                log_var_style = output['style_log_covariance']

                if self.training:
                    c_x = sample_gaussian(mu, log_var)
                    e_x = sample_gaussian(mu_style, log_var_style)
                else:
                    c_x, e_x = mu, mu_style

                reconstructions[cond_mod] = {}

                for recon_mod in inputs:
                    if recon_mod in self.used_modalities:
                        if recon_mod not in ['fmri', 'eeg']:
                            brain_target = s * 0
                        else:
                            brain_target = s

                        # Self-reconstruction
                        if recon_mod == cond_mod:
                            z_x = torch.cat([c_x, e_x, brain_target], dim=-1)

                        # Cross modal reconstruction
                        else:
                            # only keep the shared latent and generate private from prior
                            mu_prior_mod = torch.cat(
                                [self.mean_priors[recon_mod]] * len(mu), axis=0
                            )
                            sigma_prior_mod = torch.cat(
                                [self.logvars_priors[recon_mod]] * len(mu),
                                axis=0,
                            )

                            if self.training:
                                e = sample_gaussian(mu_prior_mod, sigma_prior_mod)
                            else:
                                e = mu_prior_mod
                            z_x = torch.cat([c_x, e, brain_target], dim=-1)
                        # Decode

                        z = z_x.reshape(-1, z_x.shape[-1])
                        recon = self.decoding(z, recon_mod)
                        recon = recon.reshape((*z_x.shape[:-1], *recon.shape[1:]))
                        reconstructions[cond_mod][recon_mod] = recon

                embeddings[cond_mod] = {"c": c_x, "e": e_x,
                                        "c_mean":mu, "c_logvar":log_var, 
                                        "e_mean":mu_style, "e_logvar":log_var_style}
                c_mu[cond_mod] = mu
                e_mu[cond_mod] = mu_style
     
        
        # ---------------decoding from poe fusion---------------
        embeddings['fusion'] = {'c':c_fusion}
        c_mu['fusion'] = c_fusion
        reconstructions['fusion'] = defaultdict(dict)

        for recon_mod in inputs:
            if recon_mod in self.used_modalities:
                e = embeddings[recon_mod]['e']
                if recon_mod not in ['fmri', 'eeg']:
                    brain_target = s * 0
                else:
                    brain_target = s
                z_x = torch.cat([c_fusion, e, brain_target], dim=-1)
                z = z_x.reshape(-1, z_x.shape[-1])
                recon = self.decoding(z, recon_mod)
                recon = recon.reshape((*z_x.shape[:-1], *recon.shape[1:]))
                #reconstructions['fusion'][recon_mod]['post'] = recon
                reconstructions['fusion'][recon_mod] = recon
        

        # ---------------decoding from masked fusion---------------
        for masked_keys, masked_embed in embeddings['masked_fusion'].items():
            reconstructions[f'masked_fusion_{masked_keys}'] = defaultdict(dict)
            for recon_mod in inputs:
                if recon_mod in self.used_modalities:
                    e = embeddings[recon_mod]['e']
                    if recon_mod not in ['fmri', 'eeg']:
                        brain_target = s * 0
                    else:
                        brain_target = s
                    z_x = torch.cat([masked_embed, e, brain_target], dim=-1)
                    z = z_x.reshape(-1, z_x.shape[-1])
                    recon = self.decoding(z, recon_mod)
                    recon = recon.reshape((*z_x.shape[:-1], *recon.shape[1:]))
                    reconstructions[f'masked_fusion_{masked_keys}'][recon_mod] = recon

        
        #-----------------Decoupled e classification----------------------
        mod_predict_loss = 0
        if self.multiple_latent_spaces:
            mod_embeddings = []
            mod_labels = []
            for cond_mod in self.used_modalities:
                    if cond_mod in embeddings.keys():
                        mod_embeddings.append(embeddings[cond_mod]["e"])
                        num = embeddings[cond_mod]["e"].shape[0]
                        mod_labels.extend([self.used_modalities.index(cond_mod)]*num)
            #print('gt mod', mod_labels[-10:])
            mod_embeddings = torch.cat(mod_embeddings)
            mod_labels = torch.tensor(mod_labels).to(mod_embeddings.device)

            mod_pre = self.decoders['mod'](mod_embeddings)
            mod_predict_loss = 1*nn.CrossEntropyLoss()(mod_pre, mod_labels)
            #print('mod_predict_loss', mod_predict_loss)
         
            pred_labels = torch.argmax(mod_pre, dim=1)
            accuracy = (pred_labels == mod_labels).float().mean()
            #print('mod_index accuracy', accuracy)

        return embeddings, reconstructions, c_mu, e_mu, mod_predict_loss, mod_labels


    def forward(self, x):
        #print('--------vae version--------')
        
        embeddings, reconstructions, c_mu,  e_mu, mod_predict_loss, mod_labels = (
                self.compute_posteriors_and_embeddings(x)
            )
        
        if self.training:
            loss_output =  self.loss_func(embeddings, reconstructions, x)
        else:
            loss_output = ModelOutput()

        
        loss_output["zss"] = c_mu
        loss_output["recon"] = reconstructions
        loss_output["sample_k"] = False
        loss_output['modality_mu'] = e_mu # before sample
        loss_output['mod_predict_loss'] = mod_predict_loss 
        loss_output['mod_labels'] = mod_labels
        loss_output['embeddings'] = embeddings
        return loss_output

    
    def loss_func(self, embeddings, reconstructions, inputs):
        loss_mse = 0

        for con_key, recon_per_mod in reconstructions.items():
            for recon_key, pred in recon_per_mod.items():
                gt = inputs[recon_key]

                if recon_key in ['image', 'text']:
                    gt = nn.functional.normalize(gt, dim=-1)
                    pred = nn.functional.normalize(pred, dim=-1)
                    loss_mse += 10000 * nn.MSELoss()(pred, gt)
                else:
                    loss_mse += 2 * nn.MSELoss()(pred, gt)

        kl_loss = 0
        loss_union = 0
        c = embeddings['fusion']['c']
        for key, value in embeddings.items():
            if 'fusion' not in key:
                kl_loss += calc_kld_loss(value['c_mean'], value['c_logvar'])
                kl_loss += calc_kld_loss(value['e_mean'], value['e_logvar'])
                loss_union += nn.MSELoss()(value['c'], c)
        loss_union = 10 * loss_union + 1e-5 * kl_loss

        loss_total = loss_mse + loss_union

        return ModelOutput(loss=loss_total.sum(), loss_sum=loss_total.sum(), metrics=dict())
    

    def forward_encoder(self, voxel, modality, subj_id):

        if modality in ['fmri', 'eeg']:
            s = nn.functional.one_hot(subj_id, num_classes=self.len_subj).float()
            voxel = torch.cat([voxel, s], 1)
        embedding = self.encoding(voxel, modality, style_dim=self.modalities_specific_dim, subj_dim=self.model_config.sub_dim)
        return embedding

    def forward_decoder(self, voxel, modality, subj_id):

        mu_prior_mod = torch.cat(
                        [self.mean_priors[modality]] * len(voxel), axis=0)
        subj = nn.functional.one_hot(torch.tensor([subj_id]).cuda(), num_classes=self.len_subj).float()
        if modality not in ['fmri', 'eeg']:
            subj *= 0
        voxel = torch.cat([voxel, mu_prior_mod, subj], axis=1)
        recon = self.decoding(voxel, modality)
        return recon