from abc import abstractmethod
import os
import math
import numpy as np
import torch
import torch.nn as nn
from tqdm import tqdm
import wandb
from tensorboardX import SummaryWriter

# tf32 data type is faster than standard float32
torch.backends.cuda.matmul.allow_tf32 = True

# Custom models and functions #
import utils

class Trainer:
    def __init__(self, args, accelerator, voxel2clip, device, data) -> None:
        # train logs path
        self.outdir = os.path.abspath(f'./train_logs_fmri_eeg/{args.exp_name}')
        if not os.path.exists(self.outdir):
            os.makedirs(self.outdir,exist_ok=True)
        
        self.args = args
        self.accelerator = accelerator
        self.voxel2clip = voxel2clip
        self.device = device
        self.data = data
        self.num_devices = max(torch.cuda.device_count(), 1)
        self.epoch_start = 0

        self.prepare_dataloader()
        self.prepare_optimizer()
        self.prepare_scheduler()
        self.prepare_multi_gpu()

        fmri_subj_tensor = torch.tensor(self.args.fmri_subj_list).to(self.device)
        self.sorted_fmri_subj, _ = torch.sort(fmri_subj_tensor)
        eeg_subj_tensor = torch.tensor(self.args.eeg_subj_list).to(self.device)
        self.sorted_eeg_subj, _ = torch.sort(eeg_subj_tensor)

        self.train_fmri_i=0
        self.train_eeg_i=0
        self.val_fmri_i=0
        self.val_eeg_i=0

    @abstractmethod
    def prepare_dataloader(self):
        pass

    def prepare_optimizer(self,):
        no_train_parts = ['discriminator']
        params_for_training = {n: p for n, p in self.voxel2clip.named_parameters() 
                       if not any(part in n for part in no_train_parts)}

        # Prepare optimizer
        no_decay = ['bias', 'Norm', 'temperature']
        opt_grouped_parameters = [
            {'params': [p for n, p in params_for_training.items() if not any(nd in n for nd in no_decay)], 'weight_decay': 1e-2},
            {'params': [p for n, p in params_for_training.items() if any(nd in n for nd in no_decay)], 'weight_decay': 0.0}
        ]
        self.optimizer = torch.optim.AdamW(opt_grouped_parameters, lr=self.args.max_lr)

    def prepare_scheduler(self):
        # prepare lr scheduler
        one_epoch_steps = self.num_batches
        if self.accelerator.state.deepspeed_plugin is not None: # Multi GPU
            one_epoch_steps = math.ceil(one_epoch_steps / self.num_devices)
        total_steps = self.args.num_epochs * one_epoch_steps
        print("one_epoch_steps_per_gpu:",one_epoch_steps)
        print("total_steps:",total_steps)

        if self.args.lr_scheduler_type == 'linear':
            self.lr_scheduler = torch.optim.lr_scheduler.LinearLR(
                self.optimizer,
                total_iters=total_steps,
                last_epoch=-1
            )
        elif self.args.lr_scheduler_type == 'cycle':
            self.lr_scheduler = torch.optim.lr_scheduler.OneCycleLR(
                self.optimizer, 
                max_lr=self.args.max_lr,
                total_steps=total_steps,
                final_div_factor=100,
                last_epoch=-1, 
                pct_start=2/self.args.num_epochs,
            )

    def prepare_wandb(self, local_rank, args):
        ## Weights and Biases
        if local_rank==0 and args.wandb_log: # only use main process for wandb logging
            import wandb
            wandb_run = args.exp_name
            wandb_notes = ''
            
            print(f"Wandb project {args.wandb_project} run {wandb_run}")
            wandb.login(host='https://api.wandb.ai')
            wandb_config = vars(args)
            print("wandb_config:\n",wandb_config)
            if args.resume: # wandb_auto_resume
                if args.resume_id is None:
                    args.resume_id = args.exp_name
                print("wandb_id:", args.resume_id)
                wandb.init(
                    id = args.resume_id,
                    project=args.wandb_project,
                    name=wandb_run,
                    config=wandb_config,
                    notes=wandb_notes,
                    resume="allow",
                )
            else:
                wandb.init(
                    project=args.wandb_project,
                    name=wandb_run,
                    config=wandb_config,
                    notes=wandb_notes,
                )
            log_dir = os.path.join("./runs", args.exp_name or "default_run")
            os.makedirs(log_dir, exist_ok=True)
            self.writer = SummaryWriter(log_dir=log_dir)

    @abstractmethod
    def prepare_multi_gpu(self):
        pass


    def train(self, local_rank):
        epoch = self.epoch_start
        self.losses, self.val_losses, self.lrs = [], [], []
        self.best_sim = 0
        self.best_epoch = 0

        self.val_voxel0 = self.val_image0 = None

        ## Main loop
        print(f"{self.args.exp_name} starting with epoch {epoch} / {self.args.num_epochs}")
        progress_bar = tqdm(range(epoch, self.args.num_epochs), disable=(local_rank!=0))

        for epoch in progress_bar:
            self.voxel2clip.train()
            self.sims_fmri_image = 0.
            self.sims_fmri_text = 0.
            self.sims_eeg_image = 0.
            self.sims_eeg_text = 0.
            self.val_sims_fmri_image = 0.
            self.val_sims_fmri_text = 0.
            self.val_sims_eeg_image = 0.
            self.val_sims_eeg_text = 0.

            self.fwd_percent_correct_fmri = 0.
            self.bwd_percent_correct_fmri = 0.
            self.fwd_percent_correct_eeg = 0.
            self.bwd_percent_correct_eeg = 0.
            self.val_fwd_percent_correct_fmri = 0.
            self.val_bwd_percent_correct_fmri = 0.
            self.val_fwd_percent_correct_eeg = 0.
            self.val_bwd_percent_correct_eeg = 0.

            self.loss_mse_image_sum = 0.
            self.loss_mse_text_sum = 0.
            self.loss_rec_fmri_sum = 0.
            self.loss_rec_eeg_sum = 0.

            self.val_loss_mse_image_sum = 0.
            self.val_loss_mse_text_sum = 0.
            self.val_loss_rec_fmri_sum = 0.
            self.val_loss_rec_eeg_sum = 0.

            self.union_loss = 0.
            self.val_union_loss = 0.

            self.train_fmri_i = 0.
            self.train_eeg_i = 0.
            self.val_fmri_i = 0.
            self.val_eeg_i = 0.

            # wandb logging
            self.train_epoch(epoch)
            self.log_train()

            if epoch % self.args.eval_interval == 0:
                self.eval_epoch(epoch)
                self.log_val()
            
            if self.args.wandb_log and local_rank==0:
                wandb.log(self.logs)

                for tag, value in self.logs.items():
                    self.writer.add_scalar(tag, value, global_step=self.logs["train/num_steps"])                

            progress_dict = {
                "epoch": epoch,
                "lr": self.logs["train/lr"],
                "loss": self.logs["train/loss"],
            }
            
            progress_bar.set_postfix(progress_dict)

            # Main process
            if local_rank==0:
                # # Uploading logs to wandb
                # if self.args.wandb_log:
                #     wandb.log(self.logs)
                # Save model
                if epoch % self.args.ckpt_interval == 0 or epoch == self.args.num_epochs-1:
                    self.save(epoch)

            # wait for other GPUs to catch up if needed
            self.accelerator.wait_for_everyone()

    @abstractmethod
    def train_epoch(self, epoch):
        pass
    
    @abstractmethod
    def eval_epoch(self, epoch):
        pass

    @abstractmethod
    def train_step(self, x):
        pass

    @abstractmethod
    def eval_step(self, x):
        pass
         
    def vis(self,):
        pass

    def save_ckpt(self, tag, epoch):
        ckpt_path = self.outdir+f'/{tag}.pth'
        print(f'saving {ckpt_path}',flush=True)
        unwrapped_model = self.accelerator.unwrap_model(self.voxel2clip)
        try:
            torch.save({
                'epoch': epoch,
                'model_state_dict': unwrapped_model.state_dict(),
                'optimizer_state_dict': self.optimizer.state_dict(),
                'lr_scheduler': self.lr_scheduler.state_dict(),
                'train_losses': self.losses,
                'val_losses': self.val_losses,
                'lrs': self.lrs,
                }, ckpt_path)
        except:
            print("Couldn't save... moving on to prevent crashing.")
        del unwrapped_model

    def save(self, epoch):
        self.save_ckpt(f'last', epoch)
        # save best model
        current_sim = (self.val_sims_fmri_image + self.val_sims_fmri_text) / (self.val_i + 1)
        if current_sim > self.best_sim:
            self.best_sim = current_sim
            self.best_epoch = epoch
            self.save_ckpt(f'best', epoch)
        else:
            print(f'Not best - current_similarity: {current_sim:.3f} @ epoch {epoch}, best_similarity: {self.best_sim:.3f} @ epoch {self.best_epoch}')
                
    def load(self,):
        print("\n---load from ckpt: {}---\n".format(self.args.load_from))
        checkpoint = torch.load(self.args.load_from, map_location='cpu')
        self.voxel2clip.load_state_dict(checkpoint['model_state_dict'], strict=False)
        print("loaded keys", checkpoint['model_state_dict'].keys())
        del checkpoint

    def resume(self,):
        print("\n---resuming from last.pth ckpt---\n")
        checkpoint = torch.load(self.outdir+'/last.pth', map_location='cpu')
        self.epoch_start = checkpoint['epoch']
        print("Resume at Epoch", self.epoch_start)
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.lr_scheduler.load_state_dict(checkpoint['lr_scheduler'])

        self.voxel2clip.load_state_dict(checkpoint['model_state_dict'])
        del checkpoint
    
    def log_train(self):
        self.logs = {
            "train/loss": np.mean(self.losses[-(self.train_i+1):]),
            "train/lr": self.lrs[-1],
            "train/num_steps": len(self.losses),

            "train/cosine_sim_fmri_image": self.sims_fmri_image / (self.train_fmri_i + 1),
            "train/cosine_sim_fmri_text": self.sims_fmri_text / (self.train_fmri_i + 1),
            "train/cosine_sim_eeg_image": self.sims_eeg_image / (self.train_eeg_i + 1),
            "train/cosine_sim_eeg_text": self.sims_eeg_text / (self.train_eeg_i + 1),

            "train/fwd_pct_correct_fmri": self.fwd_percent_correct_fmri / (self.train_fmri_i + 1),
            "train/bwd_pct_correct_fmri": self.bwd_percent_correct_fmri / (self.train_fmri_i + 1),
            "train/fwd_pct_correct_eeg": self.fwd_percent_correct_eeg / (self.train_eeg_i + 1),
            "train/bwd_pct_correct_eeg": self.bwd_percent_correct_eeg / (self.train_eeg_i + 1),

            "train/loss_mse_image": self.loss_mse_image_sum / (self.train_i + 1),
            "train/loss_mse_text": self.loss_mse_text_sum / (self.train_i + 1),
            "train/loss_rec_fmri": self.loss_rec_fmri_sum / (self.train_fmri_i + 1),
            "train/loss_rec_eeg": self.loss_rec_eeg_sum / (self.train_eeg_i + 1),
            "train/union_loss": self.union_loss / (self.train_i + 1),
        }

    def log_val(self):
        self.logs.update({
            "val/loss": np.mean(self.val_losses[-(self.val_i+1):]),
            "val/num_steps": len(self.val_losses),

            "val/cosine_sim_fmri_image": self.val_sims_fmri_image / (self.val_fmri_i + 1),
            "val/cosine_sim_fmri_text": self.val_sims_fmri_text / (self.val_fmri_i + 1),
            "val/cosine_sim_eeg_image": self.val_sims_eeg_image / (self.val_eeg_i + 1),
            "val/cosine_sim_eeg_text": self.val_sims_eeg_text / (self.val_eeg_i + 1),

            "val/val_fwd_pct_correct_fmri": self.val_fwd_percent_correct_fmri / (self.val_fmri_i + 1),
            "val/val_bwd_pct_correct_fmri": self.val_bwd_percent_correct_fmri / (self.val_fmri_i + 1),
            "val/val_fwd_pct_correct_eeg": self.val_fwd_percent_correct_eeg / (self.val_eeg_i + 1),
            "val/val_bwd_pct_correct_eeg": self.val_bwd_percent_correct_eeg / (self.val_eeg_i + 1),

            "val/loss_mse_image": self.val_loss_mse_image_sum / (self.val_i + 1),
            "val/loss_mse_text": self.val_loss_mse_text_sum / (self.val_i + 1),
            "val/loss_rec_fmri": self.val_loss_rec_fmri_sum / (self.val_fmri_i + 1),
            "val/loss_rec_eeg": self.val_loss_rec_eeg_sum / (self.val_eeg_i + 1),
            "val/union_loss": self.val_union_loss / (self.val_i + 1),
        })
    
    def __del__(self):
        if self.writer is not None:
            self.writer.close()



  

