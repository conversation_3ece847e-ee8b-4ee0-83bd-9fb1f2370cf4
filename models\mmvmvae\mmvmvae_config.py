from typing import Literal

from pydantic.dataclasses import dataclass

from ..base import BaseMultiVAEConfig

@dataclass
class MMVMVAEConfig(BaseMultiVAEConfig):
    """
    配置类用于MMVMVAE模型
    
    Args:
        beta (float): KL散度项的权重系数
        alpha_annealing (bool): 是否使用alpha退火
        init_alpha_value (float): alpha退火的初始值
        final_alpha_value (float): alpha退火的最终值 
        alpha_annealing_steps (int): alpha退火的步数
        temp_annealing (Literal): 温度退火方式,可选['cosine','linear','exp']
    """
    name: str = "mixedprior"
    beta: float = 1.0
    alpha_annealing: bool = False
    init_alpha_value: float = 1.0
    final_alpha_value: float = 0.0
    alpha_annealing_steps: int = 150000
    temp_annealing: Literal['cosine','linear','exp'] = 'cosine'
