import os
import torch
import numpy as np
from PIL import Image
from torch import nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import utils
import glob

from collections import defaultdict
class NSDEEGDataset(Dataset):
    def __init__(self, subject, mode, extensions=None, eeg_size=8192, length=None, load_image=False, use_mean_feature=False):
      
        self.eeg_size = eeg_size
    
        self.mode=mode
        self.sub=int(subject)
        
        self.load_image = load_image
        if self.load_image: #and self.mode=='test':
            self.samples = self._load_samples()
            self.samples_keys = sorted(self.samples.keys())

        #self.eeg_list = glob.glob(os.path.join('/opt/data/private/lq/data/nsd-fmri-eeg/eeg/', f'0{self.sub}', self.mode, '*.npz'))
        self.use_mean_feature = use_mean_feature
        if self.use_mean_feature:
            self.eeg_list = glob.glob(os.path.join('/opt/data/private/mindbridge/data/NSD/nsd-fmri-eeg/eeg_vit/', f'0{self.sub}', self.mode, '*.npz'))
        else:
            self.eeg_list = glob.glob(os.path.join('/home/<USER>/nsd-fmri-eeg/eeg/', f'0{self.sub}', self.mode, '*.npz'))

        self.eeg_list.sort()
        self.length = len(self.eeg_list)
        if length is not None:
            if self.length > length:
                self.length = length
        self.size = self.length
    
    def _load_samples(self):
        root_dir = f'/root/workspace/mindbridge/data/NSD/webdataset_avg_new/{self.mode}/subj01'
        files = os.listdir(root_dir)
        samples = defaultdict(dict)
        for file in files:
            file_path = os.path.join(root_dir, file)
            sample_id, ext = file.split(".",maxsplit=1)
            if ext == 'jpg':
                samples[sample_id][ext] = file_path
        return samples
    
    def _load_image(self, image_path):
        image = Image.open(image_path).convert('RGB')
        image = np.array(image).astype(np.float32) / 255.0
        image = torch.from_numpy(image.transpose(2, 0, 1))
        return image
    
    def vox_process(self, x):
        x = torch.mean(x.float(), axis=0)
        return x
    
    def __len__(self):
        # return len(self.samples_keys)
        return self.length

    def __getitem__(self, idx):
        idx = idx % self.length

        eeg_data = np.load(self.eeg_list[idx])
        
        x = torch.from_numpy(eeg_data['eeg'])
        x = self.vox_process(x)

        items = {}
        items['eeg'] = x
        items['image'] = eeg_data['image'].astype(np.float32)
        items['text'] = eeg_data['text'].astype(np.float32)
        items['subj'] = self.sub

        # if self.use_mean_feature:
        #     items['image']=items['image'][0]
        #     items['text']=items['text'][0]

        # if self.use_mean_feature:
        #     items['image'] = np.mean(items['image'], axis=0, keepdims=True)
        #     items['text'] = np.mean(items['text'], axis=0, keepdims=True)


        if self.load_image: # and self.mode=='test':
            sample_key = self.samples_keys[idx]
            sample = self.samples[sample_key]
            items['jpg'] = self._load_image(sample['jpg'])

        return items

def get_eeg_dataset(subject, data_path, eeg_size, length, seed, use_mean_feature):
    utils.seed_everything(seed)
    train_data = NSDEEGDataset(
        subject,
        'train',
        eeg_size=eeg_size,
        length=length,
        use_mean_feature=use_mean_feature,
    )

    val_data = NSDEEGDataset(
        subject,
        'test',
        eeg_size=eeg_size,
        use_mean_feature=use_mean_feature,
    )

    num_train=len(train_data)
    num_val=len(val_data)
    print(f"number of eeg_{subject} train data:", num_train)
    print(f"number of eeg_{subject} val data:", num_val)
    return train_data, val_data