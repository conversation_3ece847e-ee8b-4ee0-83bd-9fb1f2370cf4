import torch
import math
import torch.nn as nn

import torch.nn.functional as F
from torch.distributions import Laplace, Normal
from pythae.models.base.base_utils import ModelOutput

from models.base import BaseModel

class MMVAE(BaseModel):
    def __init__(self, model_config, encoders, decoders) -> None:
        super().__init__(model_config, encoders, decoders)

        print('current model is mmvae')
        self.model_config = model_config

        if model_config.prior_and_posterior_dist == "laplace_with_softmax":
            self.post_dist = Laplace
            self.prior_dist = Laplace
        elif model_config.prior_and_posterior_dist == "normal":
            self.post_dist = Normal
            self.prior_dist = Normal
        else:
            raise AttributeError(
                " The posterior_dist parameter must be "
                " either 'laplace_with_softmax' or 'normal'. "
                f" {model_config.prior_and_posterior_dist} was provided."
            )
        
        self.prior_mean = torch.nn.Parameter(
            torch.zeros(1, self.latent_dim), requires_grad=False
        )
        self.prior_log_var = torch.nn.Parameter(
            torch.zeros(1, self.latent_dim), requires_grad=model_config.learn_prior
        )
          

    def log_var_to_std(self, log_var):
        """
        For latent distributions parameters, transform the log covariance to the
        standard deviation of the distribution either applying softmax or not.
        This follows the original implementation.
        """

        if self.model_config.prior_and_posterior_dist == "laplace_with_softmax":
            return F.softmax(log_var, dim=-1) * log_var.size(-1) + 1e-6
        else:
            return torch.exp(0.5 * log_var)
    
    @property
    def pz_params(self):
        """From the prior mean and log_covariance, return the mean and standard
        deviation, either applying softmax or not depending on the choice of prior
        distribution.

        Returns:
            tuple: mean, std
        """
        mean = self.prior_mean
        if self.model_config.prior_and_posterior_dist == "laplace_with_softmax":
            std = (
                F.softmax(self.prior_log_var, dim=-1) * self.prior_log_var.size(-1)
                + 1e-6
            )
        else:
            std = torch.exp(0.5 * self.prior_log_var)
        return mean, std
        
    def forward(self, x):

        embeddings = {}
        emb_mu = {}
        qz_xs = {}
        qz_xs_detach = {}
        reconstructions = {}

        mu_mean = 0
        log_var_mean = 0
        num_modalities = 0

        for cond_mod, value in x.items():
            if cond_mod in self.used_modalities:
                output = self.encoding(value, cond_mod)
                mu, log_var = output['embedding'], output['log_covariance']

                mu_mean += mu
                log_var_mean += log_var
                num_modalities += 1

                sigma = self.log_var_to_std(log_var)
                qz_x = self.post_dist(mu, sigma)
                z_x = qz_x.rsample([self.model_config.K])
                #print('z_x.shape', z_x.shape)

                qz_x_detach = self.post_dist(mu.detach(), sigma.detach())

                reconstructions[cond_mod] = {}
                for recon_mod in x.keys():
                    if recon_mod in self.used_modalities:
                        z = z_x.reshape(-1, z_x.shape[-1])
                        #print('z.shape', z.shape)
                        recon = self.decoding(z, recon_mod)
                        #print('recon.shape', recon.shape)
                        recon = recon.reshape((*z_x.shape[:-1], *recon.shape[1:]))
                        #print('recon_.shape', recon.shape)
                        reconstructions[cond_mod][recon_mod] = recon
                
                qz_xs[cond_mod] = qz_x
                embeddings[cond_mod] = z_x
                qz_xs_detach[cond_mod] = qz_x_detach
                emb_mu[cond_mod] = mu.unsqueeze(0)
      
        if self.training:
            if self.model_config.loss == "dreg_looser":
                loss_output = self.dreg_looser(
                    qz_xs_detach, embeddings, reconstructions, x
                )
            elif self.model_config.loss == "iwae_looser":
                loss_output = self.iwae_looser(
                    qz_xs, embeddings, reconstructions, x
                )
            else:
                raise NotImplementedError()

        else:
            loss_output = ModelOutput()
        
        ##------ add fusion -------
        mu_mean /= num_modalities
        log_var_mean /= num_modalities
        sigma_mean = self.log_var_to_std(log_var_mean)
        qz_x = self.post_dist(mu_mean, sigma_mean)
        z_x = qz_x.rsample([self.model_config.K])
    
        reconstructions['fusion'] = {}
        for recon_mod in x.keys():
            if recon_mod in self.used_modalities:
                z = z_x.reshape(-1, z_x.shape[-1])
                recon = self.decoding(z, recon_mod)
                recon = recon.reshape((*z_x.shape[:-1], *recon.shape[1:]))
                reconstructions['fusion'][recon_mod] = recon
        emb_mu['fusion'] = mu_mean.unsqueeze(0)
        ##-------------------------
        
        loss_output["zss"] = emb_mu
        loss_output["recon"] = reconstructions
        loss_output["sample_k"] = True

        return loss_output
    
    def forward_recon(self, voxel, modality):
        output = self.encoding(voxel, modality)
        mu, log_var = output['embedding'], output['log_covariance']
        sigma = self.log_var_to_std(log_var)

        qu_x = self.post_dist(mu, sigma)
        z_x = qu_x.rsample([1])


        def get_recon(z_x, recon_mod):
            z = z_x.reshape(-1, z_x.shape[-1])
            recon = self.decoding(z, recon_mod)
            recon = recon.reshape((*z_x.shape[:-1], *recon.shape[1:]))
            return recon
        
        voxel2img = get_recon(z_x, 'image')[0]
        voxel2txt = get_recon(z_x, 'text')[0]
        return voxel2img, voxel2txt

    def compute_k_lws(self, qz_xs, embeddings, reconstructions, inputs):
        """Compute likelihood terms for all modalities and for all k

        returns :
            dict containing the likelihoods terms (not aggregated)
            for all modalities."""

        if hasattr(inputs, "masks"):
            # Compute the number of available modalities per sample
            n_mods_sample = torch.sum(
                torch.stack(tuple(inputs.masks.values())).int(), dim=0
            )
        else:
            n_mods_sample = torch.tensor([self.n_modalities])

        lws = {}  # to collect likelihoods

        for mod in embeddings:
            z = embeddings[mod]  # (K, n_batch, latent_dim)
            n_mods_sample = n_mods_sample.to(z.device)
            prior = self.prior_dist(*self.pz_params)

            ### Compute log p(z)
            lpz = prior.log_prob(z).sum(-1)

            ### Compute log q(z|X)

            # Get all the log(q(z|x_i))
            if hasattr(inputs, "masks"):
                # For incomplete data, we only use available modalities
                lqz_x = []
                for m in qz_xs:
                    qz = qz_xs[m].log_prob(z).sum(-1)
                    # Set the probability to 0 for unavailable modalities,
                    # so that they don't weigh in the Mixture-of-Experts
                    qz[torch.stack([inputs.masks[m] == False] * len(z))] = -torch.inf
                    lqz_x.append(qz)

                lqz_x = torch.stack(lqz_x)  # n_modalities,K,nbatch
            else:
                lqz_x = torch.stack(
                    [qz_xs[m].log_prob(z).sum(-1) for m in qz_xs]
                )  # n_modalities,K,nbatch

            # Compute the mixture of expert probability
            lqz_x = torch.logsumexp(lqz_x, dim=0) - torch.log(
                n_mods_sample
            )  # log_mean_exp

            ### Compute log p(X|z)
            lpx_z = 0
            for recon_mod in reconstructions[mod]:
                x_recon = reconstructions[mod][recon_mod]
                K, n_batch = x_recon.shape[0], x_recon.shape[1]

                lpx_z_mod = (
                    self.recon_log_probs[recon_mod](x_recon, inputs[recon_mod])
                    .view(K, n_batch, -1)
                    .mul(self.rescale_factors[recon_mod])
                    .sum(-1)
                )

                if hasattr(inputs, "masks"):
                    # cancel unavailable modalities
                    lpx_z_mod *= inputs.masks[recon_mod].float()

                lpx_z += lpx_z_mod

            ### Compute the entire likelihood
            lw = lpx_z + lpz - lqz_x

            if hasattr(inputs, "masks"):
                # cancel unavailable modalities
                lw *= inputs.masks[mod].float()

            lws[mod] = lw  # K, batch_size

        return lws, n_mods_sample

    def dreg_looser(self, qz_xs, embeddings, reconstructions, inputs):
        """
        The DreG estimation for IWAE. losses components in lws needs to have been computed on
        **detached** posteriors.

        """

        lws, n_mods_sample = self.compute_k_lws(
            qz_xs, embeddings, reconstructions, inputs
        )

        # Compute all the wk weights for individual likelihoods
        wk = {}
        with torch.no_grad():
            for mod, lw in lws.items():
                wk[mod] = (
                    lw - torch.logsumexp(lw, 0, keepdim=True)
                ).exp()  # K, n_batch

        # Compute the loss
        lws = torch.stack(
            [(lws[mod] * wk[mod]) for mod in embeddings], dim=0
        )  # n_modalities,K, n_batch
        lws = lws.sum(1)  # sum on K

        # The gradient with respect to \phi is multiplied one more time by wk
        # To achieve that, we register a hook on the latent variables z
        for mod in embeddings:

            embeddings[mod].register_hook(
                lambda grad, w=wk[mod]: w.unsqueeze(-1) * grad
            )

        # Take the mean over modalities
        lws = lws.sum(0) / n_mods_sample

        # Return the sum over the batch
        return ModelOutput(loss=-lws.sum(), loss_sum=-lws.sum(), metrics={})

    def iwae_looser(self, qz_xs, embeddings, reconstructions, inputs):

        lws, n_mods_sample = self.compute_k_lws(
            qz_xs, embeddings, reconstructions, inputs
        )

        # Transform into a tensor
        lws = torch.stack(list(lws.values()), dim=0)  # n_modalities, K, n_batch

        # Take log_mean_exp on K to compute the IWAE estimation
        lws = torch.logsumexp(lws, dim=1) - math.log(
            lws.size(1)
        )  # n_modalities, n_batch

        # Take the mean on modalities outside the log
        lws = lws.sum(0) / n_mods_sample

        # Return the sum over the batch
        return ModelOutput(loss=-lws.sum(), loss_sum=-lws.sum(), metrics={})