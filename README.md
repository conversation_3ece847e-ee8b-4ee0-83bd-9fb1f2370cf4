# Bridging the Brain–AI Gap via Explainable Representation Learning

A PyTorch implementation of a multi-modal variational autoencoder (VAE) for Integrating Different Modalities of Information.

## Overview

```
multi_vae/
├── models/ # Model architecture implementations(mmvaePlus_model_dis)
├── trainers/ # Training logic and optimizers
├── data/ # Data loading and preprocessing
├── evaluation_metrics/ # Visualization of Evaluation Metrics
├── scripts/ # Training and testing scripts
├── vis/ # Decoupled Vector UMAP Dimensionality Reduction Plot
├── train_logs_fmri_eeg/ # Training checkpoints
└── validation/ # scripts about predicting
```

This project implements a multi-modal VAE architecture that can:
- Encode and decode brain activity data (fMRI and EEG)
- Integrate with CLIP embeddings for cross-modal reconstruction
- Generate brain images from various modalities
- Support both single and multi-GPU training

## Dependencies

Key dependencies include:
- PyTorch 2.0.1
- torchvision 0.15.2
- wandb for experiment tracking
- Various scientific computing libraries (numpy, scipy, pandas)

For a complete list of dependencies, see `requirements.txt`.

## Installation

1. Clone the repository:
```bash
git clone [repository-url]
cd multi_vae
```

2. Create a virtual environment (recommended):
```bash
conda ctrate -n fmri python==3.9.6
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

## Usage

### Training

To train the model:

```bash
bash scripts/train.sh
```

### Testing

To evaluate the model:

```bash
bash scripts/test.py
```

## Model Architecture

The project implements several key components:

1. **Encoders**:
   - Brain Encoder: Processes fMRI/EEG data
   - CLIP Encoder: Handles CLIP embeddings
   - Union Encoder: Combines multiple modalities
   - Sub Encoder: Processes specific subjects

2. **Decoders**:
   - Brain Decoder: Generates brain activity
   - CLIP Decoder: Generates CLIP embeddings
   - Union Decoder: Generates combined outputs
   - Sub Decoder: Generates specific subjects

3. **VAE Components**:
   - MMVAE
   - MMVAEPLUS
   - MMVMVAE
   - MOPOE

## Training Logs

Training logs and results are organized as follows:

- `train_logs_fmri_eeg/`: Contains model checkpoints and weights
  - `{exp_name}/last.pth`: Latest model checkpoint
  - `{exp_name}/best.pth`: Best performing model checkpoint


## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

[]

## Citation

If you use this code in your research, please cite:
[Add citation information here]