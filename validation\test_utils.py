import torch
import math

def block_compute(func, block_size, *args):
    """
    - `args`: the args of function `func`
    """
    assert len(args) % 2 == 0, "The number of args must be even!"
    para_num = len(args)//2
    args = [arg.split(block_size, dim=0) for arg in args]
    I = len(args[0])
    J = len(args[para_num])
    z_rows = []
    for i in range(I):
        z_row = []
        for j in range(J):
            x = [args[k][i] for k in range(para_num)]
            y = [args[k+para_num][j] for k in range(para_num)]
            z = func(*(x+y))  # B * B
            z_row.append(z)
        z_row = torch.cat(z_row, dim=1)  # B * JB
        z_rows.append(z_row)
    z_rows = torch.cat(z_rows, dim=0)  # IB * JB
    return z_rows


def calc_squared_dist(x, y):
    """
    Squared Euclidian distances between two sets of variables
    - `x`: N1 * D
    - `y`: N2 * D
    """
    return torch.cdist(x, y) ** 2
    

def calc_bhat_dist(mu1, logvar1, mu2, logvar2, mem_limit=1e9):
    """
    Bhattacharyya distances between two sets of Gaussian distributions
    - `mu1`, `logvar1`: N1 * D
    - `mu2`, `logvar2`: N2 * D
    - `mem_limit`: the maximal memory allowed for computaion
    """
 
    def calc_bhat_dist_(mu1, logvar1, mu2, logvar2):
        N1, N2 = mu1.size(0), mu2.size(0)
        mu1 = mu1.unsqueeze(1)          # N1 * 1 * D
        logvar1 = logvar1.unsqueeze(1)  # N1 * 1 * D
        mu2 = mu2.unsqueeze(0)          # 1 * N2 * D
        logvar2 = logvar2.unsqueeze(0)  # 1 * N2 * D
        
        var1 = logvar1.exp()  # N1 * 1 * D
        var2 = logvar2.exp()  # 1 * N2 * D
        var = (var1 + var2) / 2  # N1 * N2 * D
        inv_var = 1 / var  # N1 * N2 * D
        inv_covar = inv_var.diag_embed()  # N1 * N2 * D * D
        
        ln_det_covar = var.log().sum(-1)  # N1 * N2
        ln_sqrt_det_covar12 = 0.5*(logvar1.sum(-1) + logvar2.sum(-1))  # N1 * N2
        
        mu_diff = mu1 - mu2  # N1 * N2 * D
        mu_diff_h = mu_diff.unsqueeze(-2)  # N1 * N2 * 1 * D
        mu_diff_v = mu_diff.unsqueeze(-1)  # N1 * N2 * D * 1
        
        dist = 1./8 * mu_diff_h.matmul(inv_covar).matmul(mu_diff_v).reshape(N1, N2) +\
               1./2 * (ln_det_covar - ln_sqrt_det_covar12)  # N1 * N2
        return dist
 
    block_size = int(math.sqrt(mem_limit / (mu1.size(1) * mu2.size(1))))
    return block_compute(calc_bhat_dist_, block_size, mu1, logvar1, mu2, logvar2)


# Evaluation metrics

def calc_foscttm(mu1, mu2, logvar1=None, logvar2=None):
    """
    Fraction Of Samples Closer Than the True Match
    - `mu1`, `mu2`, `logvar1`, `logvar2`: N * D
    """
    N = mu1.size(0)
    if logvar1 is None:
        dists = torch.cdist(mu1, mu2)  # N * N
    else: 
        dists = calc_bhat_dist(mu1, logvar1, mu2, logvar2)  # N * N
    
    true_match_dists = dists.diagonal().unsqueeze(-1).expand_as(dists)  # N * N
    foscttms = dists.lt(true_match_dists).sum(-1).float() / (N - 1)  # N
    return foscttms.mean().item()


label_to_color = {
    1:'red',
    2: 'green',
    3: 'blue',
    4: 'pink',
    5: 'yellow',
    6: 'black',
    7: 'purple',
    8: 'grey',
    9: 'orange',
    10: 'brown',
    11: 'cyan',
    12:'magenta',
    13: 'lime',
    14: 'teal',
    15: 'lavender',
    16: 'beige',
    17: 'tan',
    18: 'coral',
    19:'salmon',
    20: 'olive'
}