import torch
import torch.nn as nn
from .base_trainer import Trainer
from data.sampler import MultiDatasetSampler
import utils

class Trainer_bridge(Trainer):
    def __init__(self, args, accelerator, voxel2clip, device, data) -> None:
        super().__init__(args, accelerator, voxel2clip, device, data)

    def prepare_dataloader(self):
        # Prepare data and dataloader
        print("Preparing data and dataloader...")
        self.train_datasets = [] # tarin_dls contains all subjects separately
        self.val_datasets = [] # tarin_dls contains all subjects separately

        if 'fmri' in self.args.use_modalities:
            for subj in self.args.fmri_subj_list:
                train_data, val_data = self.data['fmri'](
                    subject=subj,
                    data_path=self.args.data_path,
                    pool_type=self.args.pool_type,
                    pool_num=self.args.pool_num,
                    length=self.args.length,
                    seed=self.args.seed,
                    use_mean_feature=self.args.use_mean_feature
                    # load_fmri=True,
                )
                self.train_datasets.append(train_data)
                self.val_datasets.append(val_data)

        if 'eeg' in self.args.use_modalities:
            for subj in self.args.eeg_subj_list:
                train_data, val_data = self.data['eeg'](
                    subject=subj,
                    data_path=self.args.data_path,
                    eeg_size=self.args.eeg_size,
                    length=self.args.length,
                    seed=self.args.seed,
                    use_mean_feature=self.args.use_mean_feature
                )
                self.train_datasets.append(train_data)
                self.val_datasets.append(val_data)

        train_dataset_cat = torch.utils.data.dataset.ConcatDataset(self.train_datasets)
        sampler = MultiDatasetSampler(train_dataset_cat, batch_size=self.args.batch_size, shuffle=True)
        self.train_data_loader = torch.utils.data.DataLoader(train_dataset_cat, batch_size=self.args.batch_size, sampler=sampler, 
            num_workers=self.args.num_workers, pin_memory=True)
        
        val_dataset_cat = torch.utils.data.dataset.ConcatDataset(self.val_datasets)
        sampler = MultiDatasetSampler(val_dataset_cat, batch_size=self.args.val_batch_size, shuffle=False)
        self.val_data_loader = torch.utils.data.DataLoader(val_dataset_cat, batch_size=self.args.val_batch_size, sampler=sampler, 
            num_workers=self.args.num_workers, pin_memory=True)
        
        self.num_batches = len(self.train_data_loader)

    def prepare_multi_gpu(self):
        self.voxel2clip, self.optimizer, self.lr_scheduler, self.train_data_loader, self.val_data_loader = self.accelerator.prepare(
        self.voxel2clip, self.optimizer, self.lr_scheduler, self.train_data_loader, self.val_data_loader)

    def train_epoch(self, epoch):
        # train loop
        for train_i, datas in enumerate(self.train_data_loader):
            self.train_i = train_i
            print(">>> Epoch{} | Iter{} |".format(epoch, train_i), flush=True)
            self.train_step(datas)

    def eval_epoch(self, epoch):
        print("Evaluating...")
        self.voxel2clip.eval()
        for val_i, datas in enumerate(self.val_data_loader): 
            self.val_i = val_i
            print(">>> Epoch{} | Eval{} |".format(epoch, val_i), flush=True)
            self.eval_step(datas)
    

    def train_step(self, x):
        loss = 0.
        self.optimizer.zero_grad()

        subj_id = x['subj']
        if 'fmri' in x.keys():
            self.train_fmri_i += 1
            subj_index_fmri = torch.searchsorted(self.sorted_fmri_subj, subj_id)
            subj_index = subj_index_fmri
        if 'eeg' in x.keys():
            self.train_eeg_i += 1
            subj_index_eeg = torch.searchsorted(self.sorted_eeg_subj, subj_id)
            subj_index = subj_index_eeg+self.args.len_subj_fmri    #fix the bug    
        s_drop_rate = 0.0 
        if torch.rand([]).item() < 1 - s_drop_rate:
            x['subj_index'] = subj_index

        clip_image =  x['image']
        clip_text = x['text']
        clip_image_norm = nn.functional.normalize(clip_image.flatten(1), dim=-1)
        clip_text_norm = nn.functional.normalize(clip_text.flatten(1), dim=-1)

        results = self.voxel2clip.forward(x)

        loss = results['loss']
        print(loss)

        for cond_mod, con_dict in results['recon'].items():
            if cond_mod == 'fmri' or cond_mod == 'eeg':
                for recon_mod, recon in con_dict.items():
                    if results['sample_k']:
                        recon = recon[0]
                    if cond_mod == 'fmri' and recon_mod == 'image':
                        clip_image_pred_norm = nn.functional.normalize(recon.flatten(1), dim=-1)
                        self.sims_fmri_image += nn.functional.cosine_similarity(clip_image_norm,clip_image_pred_norm).mean().item()
                        labels = torch.arange(len(clip_image_norm)).to(self.device) 
                        self.fwd_percent_correct_fmri += utils.topk(utils.batchwise_cosine_similarity(clip_image_pred_norm, clip_image_norm), labels, k=1)
                        self.bwd_percent_correct_fmri += utils.topk(utils.batchwise_cosine_similarity(clip_image_norm, clip_image_pred_norm), labels, k=1)
                        
                    if cond_mod == 'eeg' and recon_mod == 'image':
                        clip_image_pred_norm = nn.functional.normalize(recon.flatten(1), dim=-1)
                        self.sims_eeg_image += nn.functional.cosine_similarity(clip_image_norm,clip_image_pred_norm).mean().item()
                        labels = torch.arange(len(clip_image_norm)).to(self.device) 
                        self.fwd_percent_correct_eeg += utils.topk(utils.batchwise_cosine_similarity(clip_image_pred_norm, clip_image_norm), labels, k=1)
                        self.bwd_percent_correct_eeg += utils.topk(utils.batchwise_cosine_similarity(clip_image_norm, clip_image_pred_norm), labels, k=1)

                    if cond_mod == 'fmri' and recon_mod == 'text':
                        clip_text_pred_norm = nn.functional.normalize(recon.flatten(1), dim=-1)
                        self.sims_fmri_text += nn.functional.cosine_similarity(clip_text_norm,clip_text_pred_norm).mean().item()
                    if cond_mod == 'eeg' and recon_mod == 'text':
                        clip_text_pred_norm = nn.functional.normalize(recon.flatten(1), dim=-1)
                        self.sims_eeg_text += nn.functional.cosine_similarity(clip_text_norm,clip_text_pred_norm).mean().item()


        utils.check_loss(loss)
        self.accelerator.backward(loss)
        self.optimizer.step()

        self.losses.append(loss.item())
        self.lrs.append(self.optimizer.param_groups[0]['lr'])
        self.lr_scheduler.step()


    def eval_step(self, x):
        val_loss = 0.
        with torch.no_grad():
            clip_image =  x['image']
            clip_text = x['text']

            clip_image_norm = nn.functional.normalize(clip_image.flatten(1), dim=-1)
            clip_text_norm = nn.functional.normalize(clip_text.flatten(1), dim=-1)


            subj_id = x['subj']
            if 'fmri' in x.keys():
                subj_index = torch.searchsorted(self.sorted_fmri_subj, subj_id)
            if 'eeg' in x.keys():
                subj_index = torch.searchsorted(self.sorted_eeg_subj, subj_id)+self.args.len_subj_fmri
            s_drop_rate = 0.0
            if torch.rand([]).item() < 1 - s_drop_rate:
                x['subj_index'] = subj_index

            if 'fmri' in x.keys():
                self.val_fmri_i += 1
            if 'eeg' in x.keys():
                self.val_eeg_i += 1

            results = self.voxel2clip.forward(x)

            for cond_mod, con_dict in results['recon'].items():
                if cond_mod == 'fmri' or cond_mod == 'eeg':
                    for recon_mod, recon in con_dict.items():
                        if results['sample_k']:
                            recon = recon[0]
                        if cond_mod=='fmri' and recon_mod == 'image':
                            clip_image_pred_norm = nn.functional.normalize(recon.flatten(1), dim=-1)
                            self.val_sims_fmri_image += nn.functional.cosine_similarity(clip_image_norm,clip_image_pred_norm).mean().item()
                            labels = torch.arange(len(clip_image_norm)).to(self.device) 
                            self.val_fwd_percent_correct_fmri += utils.topk(utils.batchwise_cosine_similarity(clip_image_pred_norm, clip_image_norm), labels, k=1)
                            self.val_bwd_percent_correct_fmri += utils.topk(utils.batchwise_cosine_similarity(clip_image_norm, clip_image_pred_norm), labels, k=1)
                        if cond_mod=='eeg' and recon_mod == 'image':    
                            clip_image_pred_norm = nn.functional.normalize(recon.flatten(1), dim=-1)
                            self.val_sims_eeg_image += nn.functional.cosine_similarity(clip_image_norm,clip_image_pred_norm).mean().item()
                            labels = torch.arange(len(clip_image_norm)).to(self.device) 
                            self.val_fwd_percent_correct_eeg += utils.topk(utils.batchwise_cosine_similarity(clip_image_pred_norm, clip_image_norm), labels, k=1)
                            self.val_bwd_percent_correct_eeg += utils.topk(utils.batchwise_cosine_similarity(clip_image_norm, clip_image_pred_norm), labels, k=1)

                        if cond_mod=='fmri' and recon_mod == 'text':
                            clip_text_pred_norm = nn.functional.normalize(recon.flatten(1), dim=-1)
                            self.val_sims_fmri_text += nn.functional.cosine_similarity(clip_text_norm,clip_text_pred_norm).mean().item()
                        if cond_mod=='eeg' and recon_mod == 'text':    
                            clip_text_pred_norm = nn.functional.normalize(recon.flatten(1), dim=-1)
                            self.val_sims_eeg_text += nn.functional.cosine_similarity(clip_text_norm,clip_text_pred_norm).mean().item()