from validation.test_helper import TestBase
import numpy as np
import torch
import data

class Test(TestBase):
    def __init__(self, args, accelerator, voxel2clip, device) -> None:
        super().__init__(args, accelerator, voxel2clip, device)
        self.get_label()

    def get_label(self):
        clusters_dict = np.load('/root/workspace/mindbridge/src_fmri_eeg/image_names.npy', allow_pickle=True).item()
        clusters_dict[0]= clusters_dict[0] + clusters_dict[5] + clusters_dict[6] + clusters_dict[16] # 运动
        clusters_dict[1] = clusters_dict[1] + clusters_dict[2] + clusters_dict[3] + clusters_dict[7] + clusters_dict[8] + clusters_dict[11] + clusters_dict[13]
        clusters_dict[4] = clusters_dict[4] + clusters_dict[9] + clusters_dict[10]  # 动物
        clusters_dict[12] = clusters_dict[12] + clusters_dict[18]  # 食物
        clusters_dict[15] = clusters_dict[15] + clusters_dict[19] # 交通
        clusters_dict[14] = clusters_dict[14] + clusters_dict[17] # 室内

        keys_to_delete = [2,3,7,8,11,13,18,19, 9, 10, 5, 6, 17, 16]
        for key in keys_to_delete:
            if key in clusters_dict:
                del clusters_dict[key]

        clusters = []
        for label, numbers in clusters_dict.items():
            clusters.append(numbers)
        self.clusters = clusters


    def prepare_dataloader(self):
        # Prepare data and dataloader
        print("Preparing data and dataloader...")
        
        self.test_fmri_dls = []
        self.test_eeg_dls = []

        if 'fmri' in self.args.use_modalities: 
            for subj in self.args.fmri_subj_list:
                test_data = data.NSDDataset(
                    "{}/webdataset_avg_new/test/subj0{}".format(self.args.data_path, subj),
                    extensions=['nsdgeneral.npy', "subj"],
                    pool_type=self.args.pool_type,
                    pool_num=self.args.pool_num,
                    length=self.args.length,
                    use_mean_feature=self.args.use_mean_feature,
                )
                test_dl = torch.utils.data.DataLoader(test_data, batch_size=self.args.batch_size, num_workers=self.args.num_workers, pin_memory=True)
                self.test_fmri_dls.append(test_dl)

        if 'eeg' in self.args.use_modalities:
            for item, subj in enumerate(self.args.eeg_subj_list):
                test_data = data.NSDEEGDataset(
                    subj,
                    'test',
                    eeg_size=self.args.eeg_size,
                    use_mean_feature=self.args.use_mean_feature,
                )
                test_dl = torch.utils.data.DataLoader(test_data, batch_size=self.args.batch_size, num_workers=self.args.num_workers, pin_memory=True)
                self.test_eeg_dls.append(test_dl)