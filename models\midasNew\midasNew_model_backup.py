import torch
import math
import torch.nn as nn

import torch.nn.functional as F
from torch.distributions import Laplace, Normal
from pythae.models.base.base_utils import ModelOutput

from models.base import BaseModel
from models.nn.base_arch import Discriminator
from collections import defaultdict

import random

#---------------------------------------------------------------------
def exp(x, eps=1e-12):
    return (x < 0) * (x.clamp(max=0)).exp() + (x >= 0) / ((-x.clamp(min=0)).exp() + eps)

def log(x, eps=1e-12):
    return (x + eps).log()

def poe(mus, logvars):
    """
    Product of Experts
    - mus: [mu_1, ..., mu_M], where mu_m is N * K
    - logvars: [logvar_1, ..., logvar_M], where logvar_m is N * K
    """
    
    # mus = [torch.full_like(mus[0], 0)] + mus
    # logvars = [torch.full_like(logvars[0], 0)] + logvars
    
    mus_stack = torch.stack(mus, dim=1)  # N * M * K
    logvars_stack = torch.stack(logvars, dim=1)
    
    T = exp(-logvars_stack)  # precision of i-th Gaussian expert at point x
    T_sum = T.sum(1)  # N * K
    pd_mu = (mus_stack * T).sum(1) / T_sum
    pd_var = 1 / T_sum
    pd_logvar = log(pd_var)
    return pd_mu, pd_logvar  # N * K

def sample_gaussian(mu, logvar):
    std = (0.5*logvar).exp()
    eps = torch.randn_like(std)
    return mu + std*eps

def calc_kld_loss(mu, logvar):
    return (-0.5 * (1 + logvar - mu.pow(2) - logvar.exp())).sum() / mu.size(0)
#---------------------------------------------------------------------


class MIDASNEW_DIS(BaseModel):
    def __init__(self, model_config, encoders, decoders):
        super().__init__(model_config, encoders, decoders)



        # Set the priors for shared and private spaces.
        self.mean_priors = torch.nn.ParameterDict()
        self.logvars_priors = torch.nn.ParameterDict()
        self.beta = model_config.beta
        self.modalities_specific_dim = model_config.modalities_specific_dim
        self.reconstruction_option = model_config.reconstruction_option
        self.multiple_latent_spaces = True
        self.style_dims = {m: self.modalities_specific_dim for m in self.encoders}

        # modality specific priors (referred to as r distributions in paper)
        for mod in list(self.encoders.keys()):
            self.mean_priors[mod] = torch.nn.Parameter(
                torch.zeros(1, model_config.modalities_specific_dim),
                requires_grad=True,
            )
            self.logvars_priors[mod] = torch.nn.Parameter(
                torch.zeros(1, model_config.modalities_specific_dim),
                requires_grad=model_config.learn_modality_prior,
            )

        # # general prior (for the entire latent code) referred to as p in the paper
        # self.mean_priors["shared"] = torch.nn.Parameter(
        #     torch.zeros(
        #         1, model_config.latent_dim
        #     ),
        #     requires_grad=False,
        # )
        # self.logvars_priors["shared"] = torch.nn.Parameter(
        #     torch.zeros(
        #         1, model_config.latent_dim
        #     ),
        #     requires_grad=model_config.learn_shared_prior,
        # )
   
        self.objective = model_config.loss

        # -------------discriminator-------------------
        self.len_subj = model_config.len_subj
        input_size = model_config.latent_dim-model_config.sub_dim

        if self.multiple_latent_spaces:
            self.discriminator_modality = nn.ModuleDict()
            if model_config.len_subj_fmri != 0:
                self.discriminator_modality['fmri'] = Discriminator(model_config.len_subj_fmri, input_size)
            if model_config.len_subj_eeg != 0:
                self.discriminator_modality['eeg'] = Discriminator(model_config.len_subj_eeg, input_size)
            self.discriminator_modality['fusion'] = Discriminator(model_config.len_subj_eeg, input_size-model_config.modalities_specific_dim)


    def compute_posteriors_and_embeddings(self, inputs):

        # ------------ encodings for all modalities------------
        embeddings = {}
        reconstructions = {}
        c_mu = {}
        e_mu = {}

        temp = {}
        for cond_mod in inputs:
            if cond_mod in self.used_modalities:
                output = self.encoding(inputs[cond_mod], cond_mod, style_dim=self.modalities_specific_dim, subj_dim=self.model_config.sub_dim)
                temp[cond_mod] = output


        #--------------------u poe fusion (subj)----------------------
        u_mu_list = []
        u_logvar_list = []
        if 'subj_index' in inputs.keys():
            u_s = self.encoders['subj'](nn.functional.one_hot(inputs['subj_index'], num_classes=self.len_subj).float())
            u_s_mean, u_s_logvar = torch.chunk(u_s, 2, dim=-1)
            u_mu_list.append(u_s_mean)
            u_logvar_list.append(u_s_logvar)

        for key in ['fmri', 'eeg']:
            if key in inputs.keys():
                u_mu_list.append(temp[key]['subj_embedding'])
                u_logvar_list.append(temp[key]['subj_log_covariance'])
        u_fusion_mu, u_fusion_logvar = poe(u_mu_list, u_logvar_list)
        if self.training:
            u_fusion = sample_gaussian(u_fusion_mu, u_fusion_logvar)
        else:
            u_fusion = u_fusion_mu

        #---------------------c poe fusion (share)-------------------------
        c_mu_list = []
        c_logvar_list = []
        for key, value in temp.items():
            c_mu_list.append(value['embedding'])
            c_logvar_list.append(value['log_covariance'])
        c_fusion_mu, c_fusion_logvar = poe(c_mu_list, c_logvar_list)
        if self.training:
            c_fusion = sample_gaussian(c_fusion_mu, c_fusion_logvar)
        else:
            c_fusion = c_fusion_mu

        # ----------- decoding from one modality---------------
        for cond_mod in inputs:
            if cond_mod in self.used_modalities:
                output = temp[cond_mod]
                mu, log_var = output['embedding'], output['log_covariance']
                mu_style = output['style_embedding']
                log_var_style = output['style_log_covariance']

                if self.training:
                    c_x = sample_gaussian(mu, log_var)
                    e_x = sample_gaussian(mu_style, log_var_style)
                else:
                    c_x, e_x = mu, mu_style

                reconstructions[cond_mod] = {}
                if cond_mod in ['fmri', 'eeg']:
                    mu_subj, log_var_subj = poe([u_s_mean, output['subj_embedding']], [u_s_logvar, output['subj_log_covariance']])
                else:
                    mu_subj, log_var_subj = u_s_mean, u_s_logvar
                
                if self.training:
                    brain = sample_gaussian(mu_subj, log_var_subj)
                else:
                    brain = mu_subj

                for recon_mod in inputs:
                    if recon_mod in self.used_modalities:
                        if recon_mod not in ['fmri', 'eeg']:
                            brain_target = brain * 0
                        else:
                            brain_target = brain

                        # Self-reconstruction
                        if recon_mod == cond_mod:
                            z_x = torch.cat([c_x, e_x, brain_target], dim=-1)

                        # Cross modal reconstruction
                        else:
                            # only keep the shared latent and generate private from prior
                            mu_prior_mod = torch.cat(
                                [self.mean_priors[recon_mod]] * len(mu), axis=0
                            )
                            sigma_prior_mod = torch.cat(
                                [self.logvars_priors[recon_mod]] * len(mu),
                                axis=0,
                            )

                            if self.training:
                                e = sample_gaussian(mu_prior_mod, sigma_prior_mod)
                            else:
                                e = mu_prior_mod
                            z_x = torch.cat([c_x, e, brain_target], dim=-1)
                        # Decode

                        z = z_x.reshape(-1, z_x.shape[-1])
                        recon = self.decoding(z, recon_mod)
                        recon = recon.reshape((*z_x.shape[:-1], *recon.shape[1:]))
                        reconstructions[cond_mod][recon_mod] = recon

                embeddings[cond_mod] = {"c": c_x, "e": e_x, "u":brain, 
                                        "c_mean":mu, "c_logvar":log_var, 
                                        "e_mean":mu_style, "e_logvar":log_var_style,
                                        "u_mean":mu_subj, "u_logvar":log_var_subj,}
                c_mu[cond_mod] = mu
                e_mu[cond_mod] = mu_style
     
        
        # ---------------decoding from poe fusion---------------
        embeddings['fusion'] = {'c':c_fusion, 'u':u_fusion}
        c_mu['fusion'] = c_fusion
        reconstructions['fusion'] = defaultdict(dict)

        for recon_mod in inputs:
            if recon_mod in self.used_modalities:
                e = embeddings[recon_mod]['e']
                if recon_mod not in ['fmri', 'eeg']:
                    brain_target = u_fusion * 0
                else:
                    brain_target = u_fusion
                z_x = torch.cat([c_fusion, e, brain_target], dim=-1)
                z = z_x.reshape(-1, z_x.shape[-1])
                recon = self.decoding(z, recon_mod)
                recon = recon.reshape((*z_x.shape[:-1], *recon.shape[1:]))
                #reconstructions['fusion'][recon_mod]['post'] = recon
                reconstructions['fusion'][recon_mod] = recon


        # for recon_mod in inputs:
        #     if recon_mod in self.used_modalities:
        #         # only keep the shared latent and generate private from prior
        #         mu_prior_mod = torch.cat(
        #             [self.mean_priors[recon_mod]] * len(c_fusion), axis=0
        #         )
        #         sigma_prior_mod = torch.cat(
        #             [self.logvars_priors[recon_mod]] * len(c_fusion),
        #             axis=0,
        #         )

        #         w = sample_gaussian(mu_prior_mod, sigma_prior_mod)
        #         brain = embeddings[recon_mod]['s']
        #         if recon_mod not in ['fmri', 'eeg']:
        #             brain_target = brain * 0
        #         else:
        #             brain_target = brain

        #         z_x = torch.cat([c_fusion, w, brain_target], dim=-1)
        #         z = z_x.reshape(-1, z_x.shape[-1])
        #         recon = self.decoding(z, recon_mod)
        #         recon = recon.reshape((*z_x.shape[:-1], *recon.shape[1:]))
        #         reconstructions['fusion'][recon_mod]['prior'] = recon

        #-----------------Decoupled u classification----------------------
        subj_predict_loss = 0
        if 'subj_index' in inputs.keys():
            sub_pre = self.decoders['subj'](u_fusion)
            subj_predict_loss = nn.CrossEntropyLoss()(sub_pre, inputs['subj_index'])
            #print('subj_predict_loss', subj_predict_loss)

            pred_labels = torch.argmax(sub_pre, dim=1)
            accuracy = (pred_labels == inputs['subj_index']).float().mean()
            print('subj_index accuracy', accuracy)
        
        #-----------------Decoupled e classification----------------------
        mod_predict_loss = 0
        if self.multiple_latent_spaces:
            mod_embeddings = []
            mod_labels = []
            for cond_mod in self.used_modalities:
                    if cond_mod in embeddings.keys():
                        mod_embeddings.append(embeddings[cond_mod]["e"])
                        num = embeddings[cond_mod]["e"].shape[0]
                        mod_labels.extend([self.used_modalities.index(cond_mod)]*num)
            #print('gt mod', mod_labels[-10:])
            mod_embeddings = torch.cat(mod_embeddings)
            mod_labels = torch.tensor(mod_labels).to(mod_embeddings.device)

            mod_pre = self.decoders['mod'](mod_embeddings)
            mod_predict_loss = 1*nn.CrossEntropyLoss()(mod_pre, mod_labels)
            #print('mod_predict_loss', mod_predict_loss)
         
            pred_labels = torch.argmax(mod_pre, dim=1)
            accuracy = (pred_labels == mod_labels).float().mean()
            print('mod_index accuracy', accuracy)

        return embeddings, reconstructions, c_mu, subj_predict_loss, u_fusion, e_mu, mod_predict_loss, mod_labels


    def forward(self, x):
        print('--------vae version--------')
        
        embeddings, reconstructions, c_mu, subj_predict_loss, u_fusion, e_mu, mod_predict_loss, mod_labels = (
                self.compute_posteriors_and_embeddings(x)
            )
        
        if self.training:
            loss_output =  self.loss_func(embeddings, reconstructions, x)
        else:
            loss_output = ModelOutput()

        
        loss_output["zss"] = c_mu
        loss_output['subj_loss'] = subj_predict_loss
        loss_output["recon"] = reconstructions
        loss_output["sample_k"] = False
        loss_output['brain_b'] = u_fusion
        loss_output['modality_mu'] = e_mu
        loss_output['mod_predict_loss'] = mod_predict_loss 
        loss_output['mod_labels'] = mod_labels
        loss_output['embeddings'] = embeddings
        return loss_output

    
    def loss_func(self, embeddings, reconstructions, inputs):
        loss_mse = 0

        for con_key, recon_per_mod in reconstructions.items():
            for recon_key, pred in recon_per_mod.items():
                gt = inputs[recon_key]

                if recon_key in ['image', 'text']:
                    gt = nn.functional.normalize(gt, dim=-1)
                    pred = nn.functional.normalize(pred, dim=-1)
                    loss_mse += 10000 * nn.MSELoss()(pred, gt)
                else:
                    loss_mse += 2 * nn.MSELoss()(pred, gt)
                #print(recon_key, nn.MSELoss()(pred, gt))


        # for key, value in inputs.items():
        #     if key in self.used_modalities:
        #         pred_prior = reconstructions['fusion'][key]['prior']
        #         pred_post = reconstructions['fusion'][key]['post']

        #         if key in ['image', 'text']:
        #             pred_prior = nn.functional.normalize(pred_prior, dim=-1)
        #             pred_post = nn.functional.normalize(pred_post, dim=-1)
        #             value = nn.functional.normalize(value, dim=-1)

        #             loss_mse += 10000 * nn.MSELoss()(pred_prior, value)
        #             loss_mse += 10000 * nn.MSELoss()(pred_post, value)
                
        #         else:
        #             loss_mse += nn.MSELoss()(pred_prior, value)
        #             loss_mse += nn.MSELoss()(pred_post, value)


        kl_loss = 0
        loss_union = 0
        c = embeddings['fusion']['c']
        u = embeddings['fusion']['u']
        for key, value in embeddings.items():
            if key != 'fusion':
                kl_loss += calc_kld_loss(value['c_mean'], value['c_logvar'])
                kl_loss += calc_kld_loss(value['e_mean'], value['e_logvar'])
                kl_loss += calc_kld_loss(value['u_mean'], value['u_logvar'])
                loss_union += nn.MSELoss()(value['c'], c)
                loss_union += nn.MSELoss()(value['u'], u)
        loss_union = 10 * loss_union + 1e-5 * kl_loss

        loss_total = loss_mse + loss_union

        return ModelOutput(loss=loss_total.sum(), loss_sum=loss_total.sum(), metrics=dict())
    

    def forward_encoder(self, voxel, modality):
        embedding = self.encoding(voxel, modality, style_dim=self.modalities_specific_dim, subj_dim=self.model_config.sub_dim)
        return embedding

    def forward_decoder(self, voxel, modality, subj_id):

        mu_prior_mod = torch.cat(
                        [self.mean_priors[modality]] * len(voxel), axis=0)
        
        subj = self.encoders['subj'](nn.functional.one_hot(torch.tensor([subj_id]).cuda(), num_classes=self.len_subj).float())
        subj = torch.chunk(subj, 2, dim=-1)[0]

        voxel = torch.cat([voxel, mu_prior_mod, subj], axis=1)
        recon = self.decoding(voxel, modality)
        return recon