import torch
import torch.nn as nn

class ResMLP(nn.Module):
    def __init__(self, h, n_blocks, dropout=0.15):
        super().__init__()
        self.n_blocks = n_blocks
        self.mlp = nn.ModuleList([
            nn.Sequential(
                nn.Linear(h, h),
                nn.LayerNorm(h),
                nn.GELU(),
                nn.Dropout(dropout)
            ) for _ in range(n_blocks)
        ])

    def forward(self, x):
        residual = x
        for res_block in range(self.n_blocks):
            x = self.mlp[res_block](x)
            x += residual
            residual = x
        return x


class ConvModule(nn.Module):
    def __init__(self, in_channels, out_channels, in_features, out_features):
        super(ConvModule, self).__init__()
        self.conv1d = nn.Conv1d(in_channels=in_channels, out_channels=out_channels, kernel_size=1, stride=1, padding=0)
        self.relu = nn.ReLU()  # 添加ReLU激活函数
        self.fc = nn.Linear(in_features=out_channels*in_features, out_features=out_features)
        self.norm = nn.LayerNorm(out_channels*in_features)

    def forward(self, x):
        x = self.conv1d(x)
        x = x.reshape(x.size(0), -1)
        x = self.norm(x)
        x = self.relu(x)  # 在卷积层后应用ReLU激活函数
        x = self.fc(x)
        return x

class ConvTransposeModule(nn.Module):
    def __init__(self, in_channels, out_channels, in_features, out_features):
        super(ConvTransposeModule, self).__init__()
        self.conv_transpose = nn.ConvTranspose1d(in_channels=in_channels, out_channels=out_channels, kernel_size=1, stride=1)
        self.relu = nn.GELU()  # 添加ReLU激活函数
        self.norm = nn.LayerNorm(out_features*in_channels)
        self.fc = nn.Linear(in_features=in_features, out_features=out_features*in_channels)
        self.out_features = out_features

    def forward(self, x):
        x = self.fc(x)
        x = self.norm(x)
        x = self.relu(x)
        x = x.reshape(x.size(0), -1, self.out_features)
        x = self.conv_transpose(x)
        return x

class MLP(nn.Module):
    def __init__(self, features=[], hid_trans='mish', out_trans=False,
                 norm=False, hid_norm=False, drop=False, hid_drop=False):
        super(MLP, self).__init__()
        layer_num = len(features)
        assert layer_num > 1, "MLP should have at least 2 layers!"
        if norm:
            hid_norm = out_norm = norm
        else:
            out_norm = False
        if drop:
            hid_drop = out_drop = drop
        else:
            out_drop = False
        
        layers = []
        for i in range(1, layer_num):
            layers.append(nn.Linear(features[i-1], features[i]))
            if i < layer_num - 1:  # hidden layers (if layer number > 2)
                layers.append(Layer1D(features[i], hid_norm, hid_trans, hid_drop))
            else:                  # output layer
                layers.append(Layer1D(features[i], out_norm, out_trans, out_drop))
        self.net = nn.Sequential(*layers)

    def forward(self, x):
        return self.net(x)

def func(func_name):
    if func_name == 'tanh':
        return nn.Tanh()
    elif func_name == 'relu':
        return nn.ReLU()
    elif func_name == 'silu':
        return nn.SiLU()
    elif func_name == 'mish':
        return nn.Mish()
    elif func_name == 'sigmoid':
        return nn.Sigmoid()
    elif func_name == 'softmax':
        return nn.Softmax(dim=1)
    elif func_name == 'log_softmax':
        return nn.LogSoftmax(dim=1)
    else:
        assert False, "Invalid func_name."
        
class Layer1D(nn.Module):
    def __init__(self, dim=False, norm=False, trans=False, drop=False):
        super(Layer1D, self).__init__()
        layers = []
        if norm == "bn":
            layers.append(nn.BatchNorm1d(dim))
        elif norm == "ln":
            layers.append(nn.LayerNorm(dim))
        if trans:
            layers.append(func(trans))
        if drop:
            layers.append(nn.Dropout(drop))
        self.net = nn.Sequential(*layers)

    def forward(self, x):
        return self.net(x)
       
class Discriminator(nn.Module):

    def __init__(self, subj_list, in_dim):
        super(Discriminator, self).__init__()

        # print('number of subj', subj_list)

        self.predictors = MLP([in_dim, 512, 256, subj_list], hid_norm="ln", hid_drop=0.2)
        self.cross_entropy_loss = nn.CrossEntropyLoss()  # log_softmax + nll
        
    def forward(self, c_all, subj):
        s_r_pre = self.predictors(c_all)
        loss = self.cross_entropy_loss(s_r_pre, subj)

        pred_labels = torch.argmax(s_r_pre, dim=1)
        accuracy = (pred_labels == subj).float().mean()
        print('accuracy', accuracy)
        loss = loss
        return loss