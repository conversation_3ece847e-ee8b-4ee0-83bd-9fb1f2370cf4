import torch
import math
import torch.nn as nn

import torch.nn.functional as F
from torch.distributions import Laplace, Normal
from pythae.models.base.base_utils import ModelOutput

from models.base import BaseModel
from models.nn.base_arch import Discriminator
from collections import defaultdict

import random
from itertools import combinations

#---------------------------------------------------------------------
def exp(x, eps=1e-12):
    return (x < 0) * (x.clamp(max=0)).exp() + (x >= 0) / ((-x.clamp(min=0)).exp() + eps)

def log(x, eps=1e-12):
    return (x + eps).log()

def poe(mus, logvars):
    """
    Product of Experts
    - mus: [mu_1, ..., mu_M], where mu_m is N * K
    - logvars: [logvar_1, ..., logvar_M], where logvar_m is N * K
    """
    
    # mus = [torch.full_like(mus[0], 0)] + mus
    # logvars = [torch.full_like(logvars[0], 0)] + logvars
    
    mus_stack = torch.stack(mus, dim=1)  # N * M * K
    logvars_stack = torch.stack(logvars, dim=1)
    
    T = exp(-logvars_stack)  # precision of i-th Gaussian expert at point x
    T_sum = T.sum(1)  # N * K
    pd_mu = (mus_stack * T).sum(1) / T_sum
    pd_var = 1 / T_sum
    pd_logvar = log(pd_var)
    return pd_mu, pd_logvar  # N * K

def sample_gaussian(mu, logvar):
    std = (0.5*logvar).exp()
    eps = torch.randn_like(std)
    return mu + std*eps

def calc_kld_loss(mu, logvar):
    return (-0.5 * (1 + logvar - mu.pow(2) - logvar.exp())).sum() / mu.size(0)
#---------------------------------------------------------------------


class MIDASNEW_SINGLE(BaseModel):
    def __init__(self, model_config, encoders, decoders):
        super().__init__(model_config, encoders, decoders)


        # Set the priors for shared and private spaces.
        self.mean_priors = torch.nn.ParameterDict()
        self.logvars_priors = torch.nn.ParameterDict()
        self.beta = model_config.beta
        self.modalities_specific_dim = model_config.modalities_specific_dim
        self.reconstruction_option = model_config.reconstruction_option
        self.multiple_latent_spaces = True
        self.style_dims = {m: self.modalities_specific_dim for m in self.encoders}

        # modality specific priors (referred to as r distributions in paper)
        for mod in list(self.encoders.keys()):
            self.mean_priors[mod] = torch.nn.Parameter(
                torch.zeros(1, model_config.modalities_specific_dim),
                requires_grad=True,
            )
            self.logvars_priors[mod] = torch.nn.Parameter(
                torch.zeros(1, model_config.modalities_specific_dim),
                requires_grad=model_config.learn_modality_prior,
            )
   
        self.objective = model_config.loss

        # -------------discriminator-------------------
        self.len_subj = model_config.len_subj
        # print("len_subj", self.len_subj)
        input_size = model_config.latent_dim

        if self.multiple_latent_spaces:
            self.discriminator_modality = nn.ModuleDict()
            if model_config.len_subj_fmri != 0:
                self.discriminator_modality['fmri'] = Discriminator(model_config.len_subj_fmri, input_size)
            if model_config.len_subj_eeg != 0:
                self.discriminator_modality['eeg'] = Discriminator(model_config.len_subj_eeg, input_size)
            self.discriminator_modality['fusion'] = Discriminator(model_config.len_subj_eeg, input_size-model_config.modalities_specific_dim)


    def compute_posteriors_and_embeddings(self, inputs):

        # ------------ encodings for all modalities------------
        embeddings = {}
        reconstructions = {}
        c_mu = {}
        e_mu = {}

        if 'subj_index' in inputs.keys():
            s = nn.functional.one_hot(inputs['subj_index'], num_classes=self.len_subj).float()
        
        temp = {}
        for cond_mod in inputs:
            if cond_mod in self.used_modalities:
                modality_input = inputs[cond_mod]
                if cond_mod in ['fmri', 'eeg']:
                    modality_input = torch.cat([modality_input, s], 1)
                output = self.encoding(modality_input, cond_mod, style_dim=self.modalities_specific_dim, subj_dim=self.model_config.sub_dim)
                temp[cond_mod] = output


        # ----------- decoding from one modality---------------
        for cond_mod in inputs:
            if cond_mod in self.used_modalities:
                output = temp[cond_mod]
                mu, log_var = output['embedding'], output['log_covariance']
                mu_style = output['style_embedding']
                log_var_style = output['style_log_covariance']

                if self.training:
                    c_x = sample_gaussian(mu, log_var)
                    e_x = sample_gaussian(mu_style, log_var_style)
                else:
                    c_x, e_x = mu, mu_style

                reconstructions[cond_mod] = {}

                recon_mod = cond_mod
                if recon_mod not in ['fmri', 'eeg']:
                    brain_target = s * 0
                else:
                    brain_target = s

                z_x = torch.cat([c_x, e_x, brain_target], dim=-1)
                z = z_x.reshape(-1, z_x.shape[-1])
                recon = self.decoding(z, recon_mod)
                recon = recon.reshape((*z_x.shape[:-1], *recon.shape[1:]))
                reconstructions[cond_mod][recon_mod] = recon

                embeddings[cond_mod] = {"c": c_x, "e": e_x,
                                        "c_mean":mu, "c_logvar":log_var, 
                                        "e_mean":mu_style, "e_logvar":log_var_style}
                c_mu[cond_mod] = mu
                e_mu[cond_mod] = mu_style
        
        # print(embeddings)
        # print(reconstructions)
     
        return embeddings, reconstructions, c_mu, e_mu


    def forward(self, x):
        #print('--------vae version--------')
        
        embeddings, reconstructions, c_mu,  e_mu = (
                self.compute_posteriors_and_embeddings(x)
            )
        
        if self.training:
            loss_output =  self.loss_func(embeddings, reconstructions, x)
        else:
            loss_output = ModelOutput()

        
        loss_output["zss"] = c_mu
        loss_output["recon"] = reconstructions
        loss_output["sample_k"] = False
        loss_output['modality_mu'] = e_mu
        loss_output['mod_predict_loss'] = 0 
        loss_output['mod_labels'] = 0
        loss_output['embeddings'] = embeddings
        return loss_output

    
    def loss_func(self, embeddings, reconstructions, inputs):
        loss_mse = 0

        for con_key, recon_per_mod in reconstructions.items():
            for recon_key, pred in recon_per_mod.items():
                gt = inputs[recon_key]

                if recon_key in ['image', 'text']:
                    gt = nn.functional.normalize(gt, dim=-1)
                    pred = nn.functional.normalize(pred, dim=-1)
                    loss_mse += 10000 * nn.MSELoss()(pred, gt)
                else:
                    loss_mse += 2 * nn.MSELoss()(pred, gt)

        kl_loss = 0
        loss_union = 0
        for key, value in embeddings.items():
            if 'fusion' not in key:
                kl_loss += calc_kld_loss(value['c_mean'], value['c_logvar'])
                kl_loss += calc_kld_loss(value['e_mean'], value['e_logvar'])
        loss_union = 10 * loss_union + 1e-5 * kl_loss

        loss_total = loss_mse + loss_union

        return ModelOutput(loss=loss_total.sum(), loss_sum=loss_total.sum(), metrics=dict())
    

    def forward_encoder(self, voxel, modality, subj_id):

        if modality in ['fmri', 'eeg']:
            s = nn.functional.one_hot(subj_id, num_classes=self.len_subj).float()
            voxel = torch.cat([voxel, s], 1)
        embedding = self.encoding(voxel, modality, style_dim=self.modalities_specific_dim, subj_dim=self.model_config.sub_dim)
        return embedding

    def forward_decoder(self, voxel, modality, subj_id):

        mu_prior_mod = torch.cat(
                        [self.mean_priors[modality]] * len(voxel), axis=0)
        subj = nn.functional.one_hot(torch.tensor([subj_id]).cuda(), num_classes=self.len_subj).float()
        if modality not in ['fmri', 'eeg']:
            subj *= 0
        voxel = torch.cat([voxel, mu_prior_mod, subj], axis=1)
        recon = self.decoding(voxel, modality)
        return recon